/**
 * 本地管理客户端适配器
 * 兼容 Authing ManagementClient 接口
 */

import { PrismaClient } from '@roasmax/database';
import { PasswordManager } from '../utils/password';
import { JWTManager } from '../utils/jwt';
import crypto from 'crypto';

// 兼容 Authing SDK 的接口类型
export interface ListGroupMembersParams {
  code: string;
  page?: number;
  limit?: number;
}

export interface ListGroupMembersResult {
  statusCode: number;
  message: string;
  data: {
    totalCount: number;
    list: Array<{
      userId: string;
      email: string;
      username: string;
      nickname: string;
      phone?: string;
      emailVerified: boolean;
      phoneVerified: boolean;
      loginsCount: number;
      lastLogin: string;
      signedUp: string;
      blocked: boolean;
      isDeleted: boolean;
      status: string;
      createdAt: string;
      updatedAt: string;
    }>;
  };
}

export interface GetUserParams {
  userId: string;
}

export interface GetUserResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface CreateUserParams {
  email: string;
  password?: string;
  username?: string;
  nickname?: string;
  phone?: string;
  emailVerified?: boolean;
  phoneVerified?: boolean;
  blocked?: boolean;
  customData?: any;
}

export interface CreateUserResult {
  statusCode: number;
  message: string;
  data: {
    userId: string;
    email: string;
    username: string;
    nickname: string;
    phone?: string;
    emailVerified: boolean;
    phoneVerified: boolean;
    blocked: boolean;
    isDeleted: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface UpdateUserParams {
  userId: string;
  email?: string;
  username?: string;
  nickname?: string;
  phone?: string;
  password?: string;
  blocked?: boolean;
  customData?: any;
}

export interface UpdateUserResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface DeleteUserParams {
  userId: string;
}

export interface DeleteUserResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface AddGroupMembersParams {
  code: string;
  userIds: string[];
}

export interface AddGroupMembersResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface KickUsersParams {
  userIds: string[];
  code: string;
}

export interface KickUsersResult {
  statusCode: number;
  message: string;
  data: any;
}

export interface GetAuthorizedResourcesParams {
  userId: string;
  namespace?: string;
  resourceType?: string;
}

export interface GetAuthorizedResourcesResult {
  statusCode: number;
  message: string;
  data: {
    totalCount: number;
    list: Array<{
      code: string;
      type: string;
      actions: string[];
    }>;
  };
}

export interface ManagementConfig {
  accessKeyId?: string;
  accessKeySecret?: string;
}

export class LocalManagementClient {
  private prisma: PrismaClient;
  private config: ManagementConfig;

  constructor(config: ManagementConfig = {}) {
    this.config = config;
    this.prisma = new PrismaClient();
  }

  /**
   * 获取租户成员列表
   */
  async listGroupMembers(params: ListGroupMembersParams): Promise<ListGroupMembersResult> {
    try {
      const { code: tenantId, page = 1, limit = 10 } = params;
      const offset = (page - 1) * limit;

      // 获取总数
      const totalCount = await this.prisma.members.count({
        where: {
          tenant_id: tenantId,
          tmp_deleted_at: null
        }
      });

      // 获取用户列表
      const members = await this.prisma.members.findMany({
        where: {
          tenant_id: tenantId,
          tmp_deleted_at: null
        },
        skip: offset,
        take: limit,
        orderBy: { tmp_created_at: 'desc' }
      });

      const list = members.map(member => ({
        userId: member.user_id,
        email: member.email,
        username: member.account,
        nickname: member.nickname,
        phone: member.phone || undefined,
        emailVerified: member.email_verified ?? true,
        phoneVerified: false,
        loginsCount: 0,
        lastLogin: member.last_login_at?.toISOString() || member.tmp_created_at?.toISOString(),
        signedUp: member.tmp_created_at?.toISOString(),
        blocked: member.user_status !== 'Activated',
        isDeleted: false,
        status: member.user_status,
        createdAt: member.tmp_created_at?.toISOString(),
        updatedAt: member.tmp_updated_at?.toISOString() || member.tmp_created_at?.toISOString(),
      }));

      return {
        statusCode: 200,
        message: '获取成功',
        data: {
          totalCount,
          list
        }
      };

    } catch (error) {
      console.error('List group members error:', error);
      throw error;
    }
  }

  /**
   * 获取用户详情
   */
  async getUser(params: GetUserParams): Promise<GetUserResult> {
    try {
      const { userId } = params;

      const member = await this.prisma.members.findFirst({
        where: {
          user_id: userId,
          tmp_deleted_at: null
        }
      });

      if (!member) {
        throw new Error('用户不存在');
      }

      const userData = {
        userId: member.user_id,
        email: member.email,
        username: member.account,
        nickname: member.nickname,
        phone: member.phone || undefined,
        emailVerified: member.email_verified ?? true,
        phoneVerified: false,
        loginsCount: 0,
        lastLogin: member.last_login_at?.toISOString() || member.tmp_created_at?.toISOString(),
        signedUp: member.tmp_created_at?.toISOString(),
        blocked: member.user_status !== 'Activated',
        isDeleted: false,
        status: member.user_status,
        createdAt: member.tmp_created_at?.toISOString(),
        updatedAt: member.tmp_updated_at?.toISOString() || member.tmp_created_at?.toISOString(),
      };

      return {
        statusCode: 200,
        message: '获取成功',
        data: userData
      };

    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }

  /**
   * 创建用户
   */
  async createUser(params: CreateUserParams): Promise<CreateUserResult> {
    try {
      const {
        email,
        password,
        username,
        nickname,
        phone,
        emailVerified = true,
        blocked = false,
        customData
      } = params;

      // 检查邮箱是否已存在
      const existingUser = await this.prisma.members.findFirst({
        where: { email }
      });

      if (existingUser) {
        throw new Error('邮箱已存在');
      }

      // 生成用户ID
      const userId = crypto.randomUUID();
      
      // 处理密码
      let passwordHash: string | undefined;
      let salt: string | undefined;
      let passwordResetRequired = false;

      if (password) {
        const result = await PasswordManager.hashPassword(password);
        passwordHash = result.hash;
        salt = result.salt;
      } else {
        // 如果没有提供密码，生成随机密码并要求重置
        const randomPassword = PasswordManager.generateRandomPassword();
        const result = await PasswordManager.hashPassword(randomPassword);
        passwordHash = result.hash;
        salt = result.salt;
        passwordResetRequired = true;
      }

      // 创建用户
      const newMember = await this.prisma.members.create({
        data: {
          id: crypto.randomUUID(),
          tenant_id: customData?.tenantId || 'default',
          user_id: userId,
          user_status: blocked ? 'Suspended' : 'Activated',
          nickname: nickname || email.split('@')[0] || '',
          email,
          account: username || email,
          admin: 0,
          password: '', // 保留字段兼容性
          password_hash: passwordHash,
          salt,
          is_migrated: false,
          password_reset_required: passwordResetRequired,
          authing_user_id: null,
          last_login_at: null,
          login_attempts: 0,
          locked_until: null,
          email_verified: emailVerified,
          phone,
          tmp_created_at: new Date(),
          tmp_updated_at: new Date(),
        }
      });

      const userData = {
        userId: newMember.user_id,
        email: newMember.email,
        username: newMember.account,
        nickname: newMember.nickname,
        phone: newMember.phone,
        emailVerified: newMember.email_verified,
        phoneVerified: false,
        blocked: newMember.user_status !== 'Activated',
        isDeleted: false,
        createdAt: newMember.tmp_created_at?.toISOString(),
        updatedAt: newMember.tmp_updated_at?.toISOString(),
      };

      return {
        statusCode: 200,
        message: '创建成功',
        data: userData
      };

    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  }

  /**
   * 更新用户
   */
  async updateUser(params: UpdateUserParams): Promise<UpdateUserResult> {
    try {
      const { userId, ...updateData } = params;

      const member = await this.prisma.members.findFirst({
        where: {
          user_id: userId,
          tmp_deleted_at: null
        }
      });

      if (!member) {
        throw new Error('用户不存在');
      }

      // 准备更新数据
      const updateFields: any = {
        tmp_updated_at: new Date()
      };

      if (updateData.email) updateFields.email = updateData.email;
      if (updateData.username) updateFields.account = updateData.username;
      if (updateData.nickname) updateFields.nickname = updateData.nickname;
      if (updateData.phone) updateFields.phone = updateData.phone;
      if (updateData.blocked !== undefined) {
        updateFields.user_status = updateData.blocked ? 'Suspended' : 'Activated';
      }

      // 处理密码更新
      if (updateData.password) {
        const { hash, salt } = await PasswordManager.hashPassword(updateData.password);
        updateFields.password_hash = hash;
        updateFields.salt = salt;
        updateFields.password_reset_required = false;
      }

      // 更新用户
      const updatedMember = await this.prisma.members.update({
        where: { user_id: userId },
        data: updateFields
      });

      const userData = {
        userId: updatedMember.user_id,
        email: updatedMember.email,
        username: updatedMember.account,
        nickname: updatedMember.nickname,
        phone: updatedMember.phone || undefined,
        emailVerified: updatedMember.email_verified ?? false,
        phoneVerified: false,
        blocked: updatedMember.user_status !== 'Activated',
        isDeleted: false,
        createdAt: updatedMember.tmp_created_at?.toISOString(),
        updatedAt: updatedMember.tmp_updated_at?.toISOString(),
      };

      return {
        statusCode: 200,
        message: '更新成功',
        data: userData
      };

    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }

  /**
   * 删除用户（软删除）
   */
  async deleteUser(params: DeleteUserParams): Promise<DeleteUserResult> {
    try {
      const { userId } = params;

      const member = await this.prisma.members.findFirst({
        where: {
          user_id: userId,
          tmp_deleted_at: null
        }
      });

      if (!member) {
        throw new Error('用户不存在');
      }

      // 软删除用户
      await this.prisma.members.update({
        where: { user_id: userId },
        data: {
          tmp_deleted_at: new Date(),
          user_status: 'Suspended'
        }
      });

      // 撤销用户所有会话
      await this.prisma.user_sessions.updateMany({
        where: { user_id: userId },
        data: { is_active: false }
      });

      return {
        statusCode: 200,
        message: '删除成功',
        data: { success: true }
      };

    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  }

  /**
   * 添加用户到租户组
   */
  async addGroupMembers(params: AddGroupMembersParams): Promise<AddGroupMembersResult> {
    try {
      const { code: tenantId, userIds } = params;

      // 更新用户的租户ID
      await this.prisma.members.updateMany({
        where: {
          user_id: { in: userIds },
          tmp_deleted_at: null
        },
        data: {
          tenant_id: tenantId,
          tmp_updated_at: new Date()
        }
      });

      return {
        statusCode: 200,
        message: '添加成功',
        data: { success: true }
      };

    } catch (error) {
      console.error('Add group members error:', error);
      throw error;
    }
  }

  /**
   * 从租户组移除用户
   */
  async kickUsers(params: KickUsersParams): Promise<KickUsersResult> {
    try {
      const { userIds } = params;

      // 软删除用户
      await this.prisma.members.updateMany({
        where: {
          user_id: { in: userIds },
          tmp_deleted_at: null
        },
        data: {
          tmp_deleted_at: new Date(),
          user_status: 'Suspended'
        }
      });

      // 撤销用户所有会话
      await this.prisma.user_sessions.updateMany({
        where: { user_id: { in: userIds } },
        data: { is_active: false }
      });

      return {
        statusCode: 200,
        message: '移除成功',
        data: { success: true }
      };

    } catch (error) {
      console.error('Kick users error:', error);
      throw error;
    }
  }

  /**
   * 获取用户授权资源
   */
  async getUserAuthorizedResources(params: GetAuthorizedResourcesParams): Promise<GetAuthorizedResourcesResult> {
    try {
      const { userId, namespace, resourceType } = params;

      // 获取用户角色
      const userRoles = await this.prisma.user_roles.findMany({
        where: {
          user_id: userId,
          OR: [
            { expires_at: null },
            { expires_at: { gt: new Date() } }
          ]
        },
        include: {
          roles: {
            include: {
              permissions: true
            }
          }
        }
      });

      const resources: Array<{ code: string; type: string; actions: string[] }> = [];

      for (const userRole of userRoles) {
        if (userRole.roles) {
          for (const permission of userRole.roles.permissions) {
            if (resourceType && permission.resource_type !== resourceType) {
              continue;
            }

            const actions = Array.isArray(permission.actions) 
              ? permission.actions 
              : JSON.parse(permission.actions as string);

            resources.push({
              code: permission.resource_code,
              type: permission.resource_type,
              actions
            });
          }
        }
      }

      return {
        statusCode: 200,
        message: '获取成功',
        data: {
          totalCount: resources.length,
          list: resources
        }
      };

    } catch (error) {
      console.error('Get user authorized resources error:', error);
      throw error;
    }
  }
}
